--cpu=Cortex-M4.fp.sp
"gd32_demo_01\startup_stm32f429xx.o"
"gd32_demo_01\main.o"
"gd32_demo_01\gpio.o"
"gd32_demo_01\dma.o"
"gd32_demo_01\usart.o"
"gd32_demo_01\stm32f4xx_it.o"
"gd32_demo_01\stm32f4xx_hal_msp.o"
"gd32_demo_01\stm32f4xx_hal_uart.o"
"gd32_demo_01\stm32f4xx_hal_rcc.o"
"gd32_demo_01\stm32f4xx_hal_rcc_ex.o"
"gd32_demo_01\stm32f4xx_hal_flash.o"
"gd32_demo_01\stm32f4xx_hal_flash_ex.o"
"gd32_demo_01\stm32f4xx_hal_flash_ramfunc.o"
"gd32_demo_01\stm32f4xx_hal_gpio.o"
"gd32_demo_01\stm32f4xx_hal_dma_ex.o"
"gd32_demo_01\stm32f4xx_hal_dma.o"
"gd32_demo_01\stm32f4xx_hal_pwr.o"
"gd32_demo_01\stm32f4xx_hal_pwr_ex.o"
"gd32_demo_01\stm32f4xx_hal_cortex.o"
"gd32_demo_01\stm32f4xx_hal.o"
"gd32_demo_01\stm32f4xx_hal_exti.o"
"gd32_demo_01\system_stm32f4xx.o"
"gd32_demo_01\ebtn.o"
"gd32_demo_01\scheduler.o"
"gd32_demo_01\led_app.o"
"gd32_demo_01\btn_app.o"
"gd32_demo_01\usart_app.o"
--strict --scatter "GD32_DEMO_01\GD32_DEMO_01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32_DEMO_01.map" -o GD32_DEMO_01\GD32_DEMO_01.axf