{"version": "2.0.0", "tasks": [{"label": "build", "type": "shell", "group": {"kind": "build", "isDefault": true}, "windows": {"command": "powershell", "args": ["-c", "make"]}, "linux": {"command": "bash", "args": ["-c", "make"]}, "osx": {"command": "bash", "args": ["-c", "make"]}}, {"label": "build & run", "type": "shell", "windows": {"command": "powershell", "args": ["-c", "make run'"]}, "linux": {"command": "bash", "args": ["-c", "'make run'"]}, "osx": {"command": "bash", "args": ["-c", "'make run'"]}}, {"label": "clean", "type": "shell", "windows": {"command": "powershell", "args": ["-c", "'make clean'"]}, "linux": {"command": "bash", "args": ["-c", "'make clean'"]}, "osx": {"command": "bash", "args": ["-c", "'make clean'"]}}]}