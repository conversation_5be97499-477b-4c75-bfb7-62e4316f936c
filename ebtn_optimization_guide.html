<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>给按键库做个小手术 - 小白也能懂的优化</title>
    <style>
        /* 基础样式 - 苹果设计风格 (简化版) */
        :root {
            --primary-color: #0071e3;
            --secondary-color: #6e6e73;
            --background-color: #f5f5f7;
            --card-background: #ffffff;
            --text-color: #1d1d1f;
            --border-radius: 12px;
            --shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            --code-background: #f2f2f7;
            --code-text: #007aff;
            --highlight: #e0f0ff;
            --highlight-text: #005bb5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif; /* 更通用的中文字体 */
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.8; /* 增加行高，更易读 */
            padding: 0;
            margin: 0;
        }

        .container {
            max-width: 800px; /* 缩小宽度，更聚焦 */
            margin: 0 auto;
            padding: 30px 15px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 36px; /* 调整字号 */
            font-weight: 600;
            margin-bottom: 10px;
        }

        h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 25px 0 10px;
            border-bottom: 2px solid var(--primary-color); /* 添加下划线 */
            padding-bottom: 5px;
            color: var(--primary-color);
        }

        h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 20px 0 8px;
            color: #333;
        }

        p, li {
            font-size: 16px; /* 调整字号 */
            margin-bottom: 12px;
            color: #555; /* 调整文字颜色 */
        }

        .subtitle {
            font-size: 18px;
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .card {
            background-color: var(--card-background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 25px;
            margin-bottom: 25px;
        }

        .step-number {
            display: inline-block;
            width: 28px;
            height: 28px;
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            line-height: 28px;
            border-radius: 50%;
            margin-right: 8px;
            font-weight: 600;
            font-size: 14px; /* 调整字号 */
        }

        code {
            font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
            background-color: var(--code-background);
            color: var(--code-text);
            padding: 2px 5px;
            border-radius: 4px;
            font-size: 14px; /* 调整字号 */
        }

        pre {
            background-color: var(--code-background);
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 12px 0;
            font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
            font-size: 13px; /* 调整字号 */
            line-height: 1.5;
        }

        ul {
            padding-left: 20px;
            margin: 12px 0;
            list-style: disc; /* 使用实心圆点 */
        }

        .highlight-box {
            background-color: var(--highlight);
            border-left: 4px solid var(--highlight-text);
            padding: 12px;
            margin: 15px 0;
            border-radius: 6px;
        }
        .highlight-box p {
             color: var(--highlight-text); /* 调整高亮区文字颜色 */
             font-weight: 500;
        }

        .analogy { /* 比喻样式 */
            background-color: #fffbe6;
            border: 1px dashed #ffe58f;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .analogy strong {
            color: #d48806;
        }

        footer {
            text-align: center;
            margin-top: 40px;
            color: var(--secondary-color);
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 600px) {
            h1 { font-size: 30px; }
            h2 { font-size: 22px; }
            .container { padding: 20px 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>给按键库做个小手术</h1>
            <p class="subtitle">让它更聪明地处理"组合按键"</p>
        </header>

        <div class="card">
            <h2>🤔 遇到的小麻烦是啥？</h2>
            <p>想象一下，我们有个很厉害的"按键管理员"（就是ebtn库）。它能认出你按了哪个按钮，还能认出你是不是同时按了好几个按钮（我们叫它"组合键"）。</p>
            
            <div class="analogy">
                <p><strong>打个比方：</strong></p>
                <ul>
                    <li>按 <strong>按钮1</strong> = 对管理员"挥挥手" 👋</li>
                    <li>按 <strong>按钮2</strong> = 对管理员"点点头"点头 🙂</li>
                    <li>同时按 <strong>按钮1 + 按钮2</strong> = 对管理员做个"秘密手势" 🤝 (比如代表"复制"操作)</li>
                </ul>
            </div>

            <p>问题来了：这个管理员有点"憨"。当你做出"秘密手势"🤝 的时候，它<strong>同时</strong>看到了你在"挥手"👋 和"点头"🙂。</p>
            
            <div class="highlight-box">
                <p><strong>结果就是：</strong> 你想让它"复制"，它却可能同时做了"挥手"和"点头"对应的其他事情，这就乱套了！</p>
            </div>
        </div>

        <div class="card">
            <h2>🎯 我们想让管理员变成啥样？</h2>
            <p>我们希望这个管理员能"长点心眼"，变得更聪明：</p>
            <ul>
                <li>当它看到你在做"秘密手势"🤝 时，就<strong>只认这个手势</strong>，别管你是不是同时也在"挥手"或"点头"。</li>
                <li>我们希望给管理员加个"开关"，可以随时切换回原来的"憨憨模式"（万一有人就喜欢那样呢？）。</li>
                <li>最重要的是，这个升级过程要悄悄地进行，用户（也就是写代码用这个库的人）感觉不到任何变化，除非他们想用这个新功能。</li>
            </ul>
        </div>

        <div class="card">
            <h2>💡 聪明的解决办法！</h2>
            <p>我们给管理员定了个新规矩，叫做"组合优先检查法"：</p>

            <div class="analogy">
                <p><strong>新规矩流程：</strong></p>
                <ol>
                    <li><strong>先看有没有"秘密手势"：</strong> 管理员先快速扫一眼，看有没有人在做"秘密手势"🤝。</li>
                    <li><strong>做标记：</strong> 如果有人在做"秘密手势"（比如是张三和李四一起做的），管理员就在小本本上记下："张三和李四正在做手势"。</li>
                    <li><strong>再看普通动作：</strong> 然后管理员再去看大家的普通动作（挥手、点头）。</li>
                    <li><strong>忽略已标记的人：</strong> 当看到张三或李四时，管理员会翻翻小本本，发现他们被标记了，就<strong>假装没看见</strong>他们的"挥手"或"点头"动作。</li>
                    <li><strong>处理未标记的人：</strong> 如果看到王五在"挥手"，小本本上没他，管理员就正常回应他的"挥手"。</li>
                    <li><strong>最后处理手势：</strong> 最后，管理员再统一处理那些"秘密手势"，执行对应的操作（比如"复制"）。</li>
                </ol>
            </div>
             <div class="highlight-box">
                <p><strong>核心思想：</strong> 优先识别并"锁定"组合操作，避免它干扰单个操作的识别。</p>
            </div>
        </div>

        <div class="card">
            <h2>🔧 "手术"过程（代码层面做了啥）</h2>
            <p>为了实现上面的"新规矩"，我们对管理员的"大脑"（也就是代码）做了几处小修改：</p>
            
            <div>
                <h3><span class="step-number">1</span> 加了个"模式开关"</h3>
                <p>我们在管理员的"控制面板"上加了个开关（代码里的<code>EBTN_CFG_COMBO_PRIORITY</code>），用来开启或关闭"组合优先"模式。</p>
                <pre>// 相当于给管理员加了个新按钮
#define EBTN_CFG_COMBO_PRIORITY (1 << 0)

// 记录管理员当前用哪个模式
uint8_t config;</pre>
            </div>

            <div>
                <h3><span class="step-number">2</span> 加了个"小本本"</h3>
                <p>我们给了管理员一个小本本（代码里的<code>combo_active</code>数组），用来记录当前哪些按钮正在参与"秘密手势"。</p>
                 <pre>// 管理员的小本本，记录谁在忙着做手势
BIT_ARRAY_DEFINE(combo_active, EBTN_MAX_KEYNUM);</pre>
            </div>

            <div>
                <h3><span class="step-number">3</span> 调整了"检查顺序"</h3>
                <p>我们修改了管理员检查按钮状态的程序（主要是<code>ebtn_process_with_curr_state</code>函数），让它按照我们上面说的"新规矩流程"来工作：先检查组合键并做标记，再检查单键（跳过已标记的），最后处理组合键。</p>
                 <pre>// 这是最关键的一步：改变检查逻辑
void ebtn_process_with_curr_state(...) {
    // ... 省略代码 ...
    
    if (/* 组合优先模式开启? */) {
        // 1. 擦掉小本本旧记录
        // 2. 检查所有组合键，谁被按下了，就在小本本上记下对应的按钮
    }
    
    // 3. 检查所有单个按钮
    for (/* 每个按钮 */) {
        if (/* 这个按钮在小本本上被标记了? */) {
            // 就跳过它，不处理
            continue; 
        }
        // 否则，正常处理这个单按钮的事件
    }
    
    // 4. 最后再统一处理所有组合按钮的事件
    
    // ... 省略代码 ...
}</pre>
            </div>
             <div>
                <h3><span class="step-number">4</span> 程序启动时告诉管理员</h3>
                <p>在你的程序刚开始运行时，需要告诉管理员一声："嘿，请打开那个"组合优先"模式开关！"</p>
                 <pre>// 在你的初始化代码里加上这句
ebtn_set_config(EBTN_CFG_COMBO_PRIORITY);</pre>
            </div>
        </div>

        <div class="card">
            <h2>🎉 升级后的效果！</h2>
             <div class="analogy">
                 <p><strong>现在：</strong></p>
                 <ul>
                     <li>你对管理员做"秘密手势"🤝（按键1+按键2）：管理员<strong>只认这个手势</strong>，执行"复制"操作。它不会再错误地认为你同时在"挥手"👋 和"点头"🙂。</li>
                     <li>你只"挥手"👋（只按按键1）：管理员会正常回应你的"挥手"。</li>
                 </ul>
            </div>
            <div class="highlight-box">
                <p><strong>好处多多：</strong></p>
                <ul>
                    <li><strong>不乱套了：</strong> 组合键和单键终于能和平共处了。</li>
                    <li><strong>不用你操心：</strong> 这个麻烦在底层解决了，你写代码时基本不用改啥。</li>
                    <li><strong>可以选：</strong> 如果你还是喜欢以前的"憨憨模式"，关掉那个开关就行。</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h2>✨ 总结一下</h2>
            <p>这次"小手术"就是让我们的按键管理员学会了"抓重点"。当复杂的"组合动作"出现时，它能优先识别，并且不被附带的"小动作"干扰。这样，按键用起来就更准确、更符合直觉了！</p>
            <p>希望这个超级简单的解释能帮到你！😄</p>
        </div>

        <footer>
            <p>© 2023 米醋电子工作室 版权所有</p>
        </footer>
    </div>
</body>
</html> 