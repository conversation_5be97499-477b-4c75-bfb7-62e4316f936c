#include "btn_app.h"
#include "ebtn.h"
#include "gpio.h"
#include <string.h> // << 添加: 包含 memcpy 和 memset 所需的头文件
#include "mydefine.h"

extern uint8_t ucLed[6];
static uint8_t led_clipboard[6] = {0}; // << 添加: 用于存储复制/剪切的 LED 状态

typedef enum
{
	USER_BUTTON_0 = 0,
	USER_BUTTON_1,
	USER_BUTTON_2,
	USER_BUTTON_3,
	USER_BUTTON_4,
	USER_BUTTON_5,
	USER_BUTTON_MAX,

	// 组合键 ID 从一个不同的基地址开始，避免冲突
	USER_BUTTON_COMBO_COPY = 0x100, // 按键0 + 按键1
	USER_BUTTON_COMBO_PASTE,		// 按键0 + 按键2
	USER_BUTTON_COMBO_CUT,			// 按键0 + 按键3
	USER_BUTTON_COMBO_MAX,
} user_button_t;

/* User defined settings */
// 为组合键定义稍长一点的参数，避免误触，仅响应单击事件
static const ebtn_btn_param_t combo_ebtn_param = EBTN_PARAMS_INIT(30, 0, 50, 500, 200, 0, 1);
// 普通按键参数
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 300, 200, 0, 2); // 允许双击

static ebtn_btn_t btns[] = {
	EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
	EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
	EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
	EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
	EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
	EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
};

// 扩展组合键定义
static ebtn_btn_combo_t btns_combo[] = {
	// 使用 RAW 初始化，只关心单击事件，并使用特定的组合键参数
	EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_COPY, &combo_ebtn_param, EBTN_EVT_MASK_ONCLICK),
	EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_PASTE, &combo_ebtn_param, EBTN_EVT_MASK_ONCLICK),
	EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_CUT, &combo_ebtn_param, EBTN_EVT_MASK_ONCLICK),
};

uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
	switch (btn->key_id)
	{
	case USER_BUTTON_0:
		return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_3);
	case USER_BUTTON_1:
		return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_2);
	case USER_BUTTON_2:
		return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_5);
	case USER_BUTTON_3:
		return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_4);
	case USER_BUTTON_4:
		return !HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_13);
	case USER_BUTTON_5:
		return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_6);
	default:
		// 对于组合键或其他未明确处理的 ID，返回 0
		return 0;
	}
}

// EBTN_EVT_ONPRESS = 0x00, /*!< 按下事件 - 检测到有效按下时发送 */
// EBTN_EVT_ONRELEASE,      /*!< 释放事件 - 检测到有效释放事件时发送 (从活动到非活动) */
// EBTN_EVT_ONCLICK,        /*!< 单击事件 - 发生有效的按下和释放事件序列时发送 */
// EBTN_EVT_KEEPALIVE,      /*!< 保持活动事件 - 按钮处于活动状态时定期发送 */

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
	// 只处理单击/双击事件
	if (evt == EBTN_EVT_ONCLICK)
	{
		uint16_t click_cnt = ebtn_click_get_count(btn);

		switch (btn->key_id)
		{
		// --- 普通按键逻辑 ---
		case USER_BUTTON_0:
			if (click_cnt == 1)
			{
				ucLed[0] = 1;
			} // 单击点亮
			else if (click_cnt == 2)
			{
				ucLed[0] = 0;
			} // 双击熄灭
			my_printf(&huart1, "USER_BUTTON_0\r\n");
			break;
		case USER_BUTTON_1:
			if (click_cnt == 1)
			{
				ucLed[1] = 1;
			}
			else if (click_cnt == 2)
			{
				ucLed[1] = 0;
			}
			my_printf(&huart1, "USER_BUTTON_1\r\n");
			break;
		case USER_BUTTON_2:
			if (click_cnt == 1)
			{
				ucLed[2] = 1;
			}
			else if (click_cnt == 2)
			{
				ucLed[2] = 0;
			}
			my_printf(&huart1, "USER_BUTTON_2\r\n");
			break;
		case USER_BUTTON_3:
			if (click_cnt == 1)
			{
				ucLed[3] = 1;
			}
			else if (click_cnt == 2)
			{
				ucLed[3] = 0;
			}
			my_printf(&huart1, "USER_BUTTON_3\r\n");
			break;
		case USER_BUTTON_4:
			if (click_cnt == 1)
			{
				ucLed[4] = 1;
			}
			else if (click_cnt == 2)
			{
				ucLed[4] = 0;
			}
			my_printf(&huart1, "USER_BUTTON_4\r\n");
			break;
		case USER_BUTTON_5:
			if (click_cnt == 1)
			{
				ucLed[5] = 1;
			}
			else if (click_cnt == 2)
			{
				ucLed[5] = 0;
			}
			my_printf(&huart1, "USER_BUTTON_5\r\n");
			break;

		// --- 组合按键逻辑 ---
		case USER_BUTTON_COMBO_COPY: // BTN0 + BTN1 (复制)
			if (click_cnt == 1)		 // 仅处理单击
			{
				memcpy(led_clipboard, ucLed, sizeof(ucLed));
				my_printf(&huart1, "Copy\r\n");
			}
			break;

		case USER_BUTTON_COMBO_PASTE: // BTN0 + BTN2 (粘贴)
			if (click_cnt == 1)
			{
				memcpy(ucLed, led_clipboard, sizeof(ucLed));
				my_printf(&huart1, "Paste\r\n");
			}
			break;

		case USER_BUTTON_COMBO_CUT: // BTN0 + BTN3 (剪切)
			if (click_cnt == 1)
			{
				memcpy(led_clipboard, ucLed, sizeof(ucLed)); // 复制状态
				memset(ucLed, 0, sizeof(ucLed));			 // 全部熄灭
				my_printf(&huart1, "Cut\r\n");
			}
			break;

		default:
			// 其他按键或未处理的点击次数
			break;
		}
	}
}

void app_ebtn_init(void)
{
	// 初始化时传递所有按键和组合键
	int init_ok = ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);

	if (!init_ok)
	{
		// printf("Error: ebtn_init failed!\n");
		// 处理初始化失败
		return;
	}
	
	// 启用组合键优先处理模式，防止组合键和单键冲突
	ebtn_set_config(EBTN_CFG_COMBO_PRIORITY);

	// --- 配置组合键成员 ---
	// 注意: 确保在 ebtn_init 之后调用
	int btn0_idx = ebtn_get_btn_index_by_key_id(USER_BUTTON_0);
	int btn1_idx = ebtn_get_btn_index_by_key_id(USER_BUTTON_1);
	int btn2_idx = ebtn_get_btn_index_by_key_id(USER_BUTTON_2);
	int btn3_idx = ebtn_get_btn_index_by_key_id(USER_BUTTON_3);

	// 配置 Copy 组合键 (BTN0 + BTN1)
	if (btn0_idx >= 0 && btn1_idx >= 0)
	{
		// 找到定义中 ID 为 USER_BUTTON_COMBO_COPY 的组合键 (通常是第 0 个)
		ebtn_combo_btn_add_btn_by_idx(&btns_combo[0], btn0_idx);
		ebtn_combo_btn_add_btn_by_idx(&btns_combo[0], btn1_idx);
	}
	else
	{
		// printf("Warning: Index not found for COMBO_COPY setup.\n");
	}

	// 配置 Paste 组合键 (BTN0 + BTN2)
	if (btn0_idx >= 0 && btn2_idx >= 0)
	{
		// 找到定义中 ID 为 USER_BUTTON_COMBO_PASTE 的组合键 (通常是第 1 个)
		ebtn_combo_btn_add_btn_by_idx(&btns_combo[1], btn0_idx);
		ebtn_combo_btn_add_btn_by_idx(&btns_combo[1], btn2_idx);
	}
	else
	{
		// printf("Warning: Index not found for COMBO_PASTE setup.\n");
	}

	// 配置 Cut 组合键 (BTN0 + BTN3)
	if (btn0_idx >= 0 && btn3_idx >= 0)
	{
		// 找到定义中 ID 为 USER_BUTTON_COMBO_CUT 的组合键 (通常是第 2 个)
		ebtn_combo_btn_add_btn_by_idx(&btns_combo[2], btn0_idx);
		ebtn_combo_btn_add_btn_by_idx(&btns_combo[2], btn3_idx);
	}
	else
	{
		// printf("Warning: Index not found for COMBO_CUT setup.\n");
	}
}

void btn_task(void)
{
	// uwTick 由 HAL 库定义和更新 (需包含正确的 HAL 头文件)
	ebtn_process(uwTick);
}
