/**
 * @file        comment_templates.h
 * @brief       中文注释标准模板定义文件
 * @details     本文件定义了项目中所有中文注释的标准格式和模板，确保注释风格的一致性
 *              包含文件头、函数、宏定义、结构体等各种代码元素的注释模板
 * <AUTHOR> AI Assistant
 * @date        2025-01-20
 * @version     1.0
 * @note        所有项目文件都应遵循此文件定义的注释标准
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>日期       <th>版本   <th>作者         <th>说明
 * <tr><td>2025-01-20 <td>1.0    <td>Kiro AI     <td>创建注释标准模板文件
 * </table>
 */

#ifndef COMMENT_TEMPLATES_H
#define COMMENT_TEMPLATES_H

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 注释风格枚举定义
 * @details 定义了三种不同详细程度的注释风格，适应不同的使用场景
 */
typedef enum {
    COMMENT_STYLE_CONCISE = 0,    ///< 简洁型：适合有经验的开发者，注释简明扼要
    COMMENT_STYLE_DETAILED = 1,   ///< 详细型：适合一般开发者，注释详细完整
    COMMENT_STYLE_TUTORIAL = 2    ///< 教学型：适合初学者，包含原理解释和示例
} comment_style_t;

/**
 * @brief 语言难度级别枚举定义
 * @details 定义了注释语言的难度级别，确保不同水平的读者都能理解
 */
typedef enum {
    LANGUAGE_LEVEL_PROFESSIONAL = 0,  ///< 专业级：使用专业术语，适合专家
    LANGUAGE_LEVEL_INTERMEDIATE = 1,  ///< 中级：平衡专业性和可读性
    LANGUAGE_LEVEL_BEGINNER = 2       ///< 初学者级：避免专业术语，详细解释概念
} language_level_t;

/**
 * @brief 注释标准配置结构体
 * @details 定义了项目中所有注释的标准格式和规范配置
 */
typedef struct {
    comment_style_t style;        ///< 注释风格：控制注释的详细程度
    language_level_t level;       ///< 语言难度：控制用词的专业程度
    uint8_t max_line_length;      ///< 最大行长度：控制注释行的长度限制
    uint8_t min_coverage_rate;    ///< 最小覆盖率：要求的注释覆盖率百分比
    uint8_t enable_examples;      ///< 启用示例：是否在注释中包含代码示例
    uint8_t enable_diagrams;      ///< 启用图表：是否在注释中包含ASCII图表
} comment_standard_t;

/**
 * @brief 注释质量检查结果结构体
 * @details 存储注释质量检查的各项指标和结果
 */
typedef struct {
    char file_path[256];          ///< 文件路径：被检查文件的完整路径
    uint8_t coverage_rate;        ///< 覆盖率：注释覆盖的函数百分比
    uint8_t completeness_score;   ///< 完整性评分：注释完整性得分(0-100)
    uint8_t readability_score;    ///< 可读性评分：注释可读性得分(0-100)
    uint8_t consistency_score;    ///< 一致性评分：注释格式一致性得分(0-100)
    uint8_t overall_score;        ///< 总体评分：综合质量评分(0-100)
    char suggestions[512];        ///< 改进建议：具体的改进建议和说明
} comment_quality_result_t;

/**
 * @brief 初始化注释标准配置
 * @details 使用默认参数初始化注释标准配置，适合大多数项目使用
 * 
 * @param[out] config 注释标准配置结构体指针，用于存储初始化后的配置
 * 
 * @return 初始化结果
 * @retval 0 初始化成功
 * @retval -1 参数错误，config为空指针
 * 
 * @note 默认配置为教学型风格，初学者语言级别，适合学习使用
 * @warning 请确保config指针有效，否则会导致程序崩溃
 * 
 * @par 示例:
 * @code
 * comment_standard_t config;
 * int result = init_comment_standard(&config);
 * if (result == 0) {
 *     printf("注释标准初始化成功\n");
 * }
 * @endcode
 */
int init_comment_standard(comment_standard_t *config);

/**
 * @brief 检查注释质量
 * @details 对指定文件的注释进行全面的质量检查，包括覆盖率、完整性、
 *          可读性和一致性等多个维度的评估
 * 
 * @param[in] file_path 要检查的文件路径，支持相对路径和绝对路径
 * @param[in] standard 注释标准配置，定义检查的标准和要求
 * @param[out] result 检查结果结构体指针，用于存储详细的检查结果
 * 
 * @return 检查执行结果
 * @retval 0 检查成功完成
 * @retval -1 文件不存在或无法访问
 * @retval -2 参数错误，指针为空
 * @retval -3 文件格式不支持
 * 
 * @note 支持检查.c和.h文件，其他格式文件会返回错误
 * @warning 大文件检查可能需要较长时间，请耐心等待
 * @see init_comment_standard() 用于初始化检查标准
 * 
 * @par 示例:
 * @code
 * comment_standard_t standard;
 * comment_quality_result_t result;
 * init_comment_standard(&standard);
 * 
 * int ret = check_comment_quality("APP/btn_app.c", &standard, &result);
 * if (ret == 0) {
 *     printf("文件: %s, 总体评分: %d\n", result.file_path, result.overall_score);
 * }
 * @endcode
 */
int check_comment_quality(const char *file_path, 
                         const comment_standard_t *standard, 
                         comment_quality_result_t *result);

/**
 * @brief 验证注释格式一致性
 * @details 检查指定文件中的注释是否符合标准格式要求，包括注释符号、
 *          缩进、换行等格式规范的一致性验证
 * 
 * @param[in] file_path 要验证的文件路径
 * @param[in] standard 注释标准配置
 * 
 * @return 验证结果
 * @retval 1 格式完全符合标准
 * @retval 0 格式基本符合，有轻微问题
 * @retval -1 格式不符合标准，需要修正
 * @retval -2 文件访问错误
 * 
 * @note 此函数只检查格式，不检查内容的准确性
 * @see check_comment_quality() 用于全面的质量检查
 */
int validate_comment_format(const char *file_path, const comment_standard_t *standard);

/**
 * @brief 生成注释覆盖率报告
 * @details 扫描指定目录下的所有源文件，统计注释覆盖率，生成详细的报告
 * 
 * @param[in] directory_path 要扫描的目录路径
 * @param[in] standard 注释标准配置
 * @param[in] output_file 报告输出文件路径，如果为NULL则输出到控制台
 * 
 * @return 生成结果
 * @retval 0 报告生成成功
 * @retval -1 目录不存在或无法访问
 * @retval -2 输出文件创建失败
 * 
 * @note 报告包含每个文件的详细统计信息和改进建议
 * @warning 大型项目的扫描可能需要较长时间
 */
int generate_coverage_report(const char *directory_path, 
                           const comment_standard_t *standard, 
                           const char *output_file);

#ifdef __cplusplus
}
#endif

#endif /* COMMENT_TEMPLATES_H */